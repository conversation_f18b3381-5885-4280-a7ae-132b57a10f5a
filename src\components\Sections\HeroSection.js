import React from 'react';
import Container from '../Layout/Container';
import useResponsive from '../../hooks/useResponsive';

const HeroSection = () => {
  const { isMobile } = useResponsive();

  return (
    <section className="relative z-10 min-h-screen flex flex-col items-center justify-center pt-20 pb-8">
      <Container size="default" padding="default">
        {/* Hero Image - Full Focus */}
        <div className="relative flex justify-center">
          <div className={`${isMobile ? 'w-80 h-96' : 'w-96 h-[500px] lg:w-[500px] lg:h-[600px]'} relative`}>
            <img
              src={isMobile ? "/BC25_Mobile.png" : "/BC25_Web.png"}
              alt="The Blessing Asia 2025"
              className="w-full h-full object-contain"
            />
            {/* Optional glow effect */}
            <div className="absolute inset-0 bg-gradient-to-t from-orange-500/10 via-transparent to-transparent rounded-lg blur-sm"></div>
          </div>
        </div>
      </Container>
    </section>
  );
};

export default HeroSection;

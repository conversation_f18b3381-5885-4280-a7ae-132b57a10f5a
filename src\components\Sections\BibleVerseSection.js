import React from 'react';
import Container from '../Layout/Container';
import useResponsive from '../../hooks/useResponsive';

const BibleVerseSection = () => {
  const { isMobile } = useResponsive();

  // Chess piece icon component (representing the trophy/crown from the images)
  const ChessIcon = () => (
    <div className="relative">
      {/* Orange glow effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-orange-500/40 to-amber-500/40 rounded-full blur-xl scale-150"></div>
      
      {/* Chess piece */}
      <div className="relative w-16 h-20 lg:w-20 lg:h-24 mx-auto">
        {/* Star on top */}
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 text-orange-400 text-xl lg:text-2xl">
          ⭐
        </div>
        
        {/* Chess piece body */}
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 w-8 h-12 lg:w-10 lg:h-14 bg-gradient-to-b from-orange-500 to-orange-600 rounded-t-full"></div>
        <div className="absolute top-12 left-1/2 transform -translate-x-1/2 w-10 h-4 lg:w-12 lg:h-5 bg-gradient-to-b from-orange-600 to-orange-700 rounded-b-lg"></div>
        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-12 h-3 lg:w-14 lg:h-4 bg-gradient-to-b from-orange-700 to-orange-800 rounded-lg"></div>
      </div>
    </div>
  );

  return (
    <section className="bg-black py-16 lg:py-20 relative overflow-hidden">
      {/* Background glow effects */}
      <div className="absolute top-1/2 left-1/4 w-96 h-96 bg-orange-500/5 rounded-full blur-3xl"></div>
      <div className="absolute top-1/2 right-1/4 w-96 h-96 bg-amber-500/5 rounded-full blur-3xl"></div>
      
      <Container>
        {isMobile ? (
          // Mobile Layout: Vertical Stack
          <div className="text-center space-y-8">
            {/* Chess Icon */}
            <div className="flex justify-center">
              <ChessIcon />
            </div>
            
            {/* Bible Verse Text */}
            <div className="space-y-6">
              {/* Chinese Text */}
              <p className="text-white text-lg lg:text-xl leading-relaxed font-medium">
                "看哪，我要做一件新事，如今要发现；你们岂不知道吗？"
              </p>
              
              {/* English Text */}
              <p className="text-white text-base lg:text-lg leading-relaxed">
                Behold, I will do a new thing; now it shall spring forth; shall ye not know it?
              </p>
              
              {/* Bible Reference */}
              <p className="text-gray-400 text-sm lg:text-base">
                以赛亚书 43:19 | Isaiah 43:19
              </p>
            </div>
          </div>
        ) : (
          // Webview Layout: Horizontal
          <div className="flex items-center justify-center space-x-12 lg:space-x-16">
            {/* Chess Icon */}
            <div className="flex-shrink-0">
              <ChessIcon />
            </div>
            
            {/* Bible Verse Text */}
            <div className="flex-1 max-w-3xl space-y-4">
              {/* Chinese Text */}
              <p className="text-white text-xl lg:text-2xl leading-relaxed font-medium">
                "看哪，我要做一件新事，如今要发现；你们岂不知道吗？"
              </p>
              
              {/* English Text */}
              <p className="text-white text-lg lg:text-xl leading-relaxed">
                Behold, I will do a new thing; now it shall spring forth; shall ye not know it?
              </p>
              
              {/* Bible Reference */}
              <p className="text-gray-400 text-base lg:text-lg">
                以赛亚书 43:19 | Isaiah 43:19
              </p>
            </div>
          </div>
        )}
      </Container>
    </section>
  );
};

export default BibleVerseSection;

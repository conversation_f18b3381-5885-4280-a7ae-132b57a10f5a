import React, { useState } from 'react';
import useResponsive from '../../hooks/useResponsive';
import MobileMenu from './MobileMenu';

const Header = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { isMobile } = useResponsive();

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMobileMenuOpen(false); // Close mobile menu after navigation
  };

  const navigationItems = [
    { id: 'speakers', label: 'Speakers & Guests', chinese: '讲员阵容' },
    { id: 'tickets', label: 'Tickets', chinese: '票价' },
    { id: 'schedules', label: 'Schedules', chinese: '日程表' },
    { id: 'masterclass', label: 'Masterclass', chinese: '' },
    { id: 'faq', label: 'FAQ', chinese: '' },
  ];

  return (
    <>
      <nav className="relative z-50 flex justify-between items-center px-4 sm:px-8 py-6 fixed top-0 left-0 right-0 bg-theme-primary/90 backdrop-blur-sm border-b border-theme-primary">
        {/* Logo */}
        <div className="bg-theme-tertiary px-4 sm:px-6 py-3 rounded">
          <span className="text-theme-primary font-semibold text-sm sm:text-base">TB Logo</span>
        </div>

        {/* Desktop Navigation */}
        {!isMobile && (
          <div className="flex space-x-4 lg:space-x-8 text-sm">
            {navigationItems.map((item) => (
              <div
                key={item.id}
                className="text-center cursor-pointer group relative"
                onClick={() => scrollToSection(item.id)}
              >
                <div className="px-3 lg:px-4 py-3 rounded-lg transition-all duration-300 hover:bg-theme-tertiary/50">
                  <div className="text-sm lg:text-base transition-colors group-hover:text-theme-primary text-theme-secondary">
                    {item.label}
                  </div>
                  {item.chinese && (
                    <div className="text-xs opacity-75 group-hover:opacity-100 transition-opacity text-theme-muted">
                      {item.chinese}
                    </div>
                  )}
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-theme-accent transition-all duration-300 group-hover:w-full"></span>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Mobile Menu Button */}
        {isMobile && (
          <button
            className="text-theme-primary p-2 rounded-lg hover:bg-theme-tertiary/50 transition-colors"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label="Toggle mobile menu"
          >
            <div className="w-6 h-6 flex flex-col justify-center items-center">
              <span className={`bg-theme-primary block transition-all duration-300 ease-out h-0.5 w-6 rounded-sm ${isMobileMenuOpen ? 'rotate-45 translate-y-1' : '-translate-y-0.5'}`}></span>
              <span className={`bg-theme-primary block transition-all duration-300 ease-out h-0.5 w-6 rounded-sm my-0.5 ${isMobileMenuOpen ? 'opacity-0' : 'opacity-100'}`}></span>
              <span className={`bg-theme-primary block transition-all duration-300 ease-out h-0.5 w-6 rounded-sm ${isMobileMenuOpen ? '-rotate-45 -translate-y-1' : 'translate-y-0.5'}`}></span>
            </div>
          </button>
        )}
      </nav>

      {/* Mobile Menu */}
      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        navigationItems={navigationItems}
        onNavigate={scrollToSection}
      />
    </>
  );
};

export default Header;
